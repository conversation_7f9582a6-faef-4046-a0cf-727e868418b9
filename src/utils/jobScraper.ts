import axios from 'axios';
import * as cheerio from 'cheerio';
import { Job, JobScrapingResult } from '@/types/job';

const BASE_URL = 'https://web3.career';

export async function scrapeWeb3Jobs(page: number = 1): Promise<JobScrapingResult> {
  try {
    const url = page === 1 ? `${BASE_URL}/java-jobs` : `${BASE_URL}/java-jobs?page=${page}`;
    
    // Note: Due to CORS restrictions, we'll need to use a proxy or server-side solution
    // For now, we'll simulate the data structure and provide a fallback
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    const $ = cheerio.load(response.data);
    const jobs: Job[] = [];

    // Parse job listings from the page
    $('tr').each((index, element) => {
      const $row = $(element);
      
      // Skip header row and empty rows
      if ($row.find('th').length > 0 || $row.find('td').length === 0) {
        return;
      }

      const $cells = $row.find('td');
      if ($cells.length >= 5) {
        const titleElement = $cells.eq(0).find('a').first();
        const companyElement = $cells.eq(1).find('h3');
        const locationText = $cells.eq(3).text().trim();
        const salaryText = $cells.eq(4).text().trim();
        const postedText = $cells.eq(2).text().trim();
        
        // Extract tags
        const tags: string[] = [];
        $cells.eq(5).find('a').each((_, tagEl) => {
          const tagText = $(tagEl).text().trim();
          if (tagText) tags.push(tagText);
        });

        const title = titleElement.text().trim();
        const company = companyElement.text().trim();
        const jobUrl = titleElement.attr('href');
        
        if (title && company && jobUrl) {
          const job: Job = {
            id: `web3-${index}-${Date.now()}`,
            title,
            company,
            location: locationText || 'Remote',
            salary: salaryText || 'Not specified',
            postedTime: postedText || 'Recently',
            tags,
            description: '', // Will be populated when fetching individual job details
            applyUrl: jobUrl.startsWith('http') ? jobUrl : `${BASE_URL}${jobUrl}`,
            companyUrl: companyElement.parent().attr('href')
          };
          
          jobs.push(job);
        }
      }
    });

    // Check for pagination
    const hasNextPage = $('a:contains("Next")').length > 0;
    const totalCountText = $('h1').text();
    const totalMatch = totalCountText.match(/(\d+,?\d*)\s+jobs/);
    const totalCount = totalMatch ? parseInt(totalMatch[1].replace(',', '')) : jobs.length;

    return {
      jobs,
      totalCount,
      currentPage: page,
      hasNextPage
    };

  } catch (error) {
    console.error('Error scraping jobs:', error);
    
    // Fallback: Return sample data for development/testing
    return getSampleJobs(page);
  }
}

export async function fetchJobDescription(jobUrl: string): Promise<string> {
  try {
    const response = await axios.get(jobUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    const $ = cheerio.load(response.data);
    
    // Extract job description - this may need adjustment based on actual HTML structure
    const description = $('.job-description, .description, [class*="description"]').first().text().trim();
    
    return description || 'Job description not available';
  } catch (error) {
    console.error('Error fetching job description:', error);
    return 'Unable to fetch job description';
  }
}

// Fallback sample data for development/testing
function getSampleJobs(page: number): JobScrapingResult {
  const allSampleJobs: Job[] = [
    // Page 1 jobs
    {
      id: 'sample-1-1',
      title: 'Lead Software Engineer',
      company: 'Mastercard',
      location: 'Pune, India',
      salary: '$81k - $84k',
      postedTime: '12h',
      tags: ['engineer', 'lead', 'dev', 'angular', 'blockchain'],
      description: 'Mastercard is looking for a talented Lead Software Development Engineer to join the Mastercard Blockchain and Digital Assets engineering team...',
      applyUrl: 'https://careers.mastercard.com/us/en/search-results'
    },
    {
      id: 'sample-1-2',
      title: 'Senior Java Developer',
      company: 'ConsenSys',
      location: 'Remote',
      salary: '$95k - $120k',
      postedTime: '8h',
      tags: ['java', 'senior', 'blockchain', 'ethereum', 'smart-contracts'],
      description: 'Join ConsenSys as a Senior Java Developer working on Ethereum infrastructure and Web3 applications...',
      applyUrl: 'https://consensys.io/careers'
    },
    {
      id: 'sample-1-3',
      title: 'Blockchain Engineer',
      company: 'Chainlink Labs',
      location: 'San Francisco, CA',
      salary: '$110k - $140k',
      postedTime: '1d',
      tags: ['blockchain', 'solidity', 'defi', 'oracles', 'smart-contracts'],
      description: 'Chainlink Labs is seeking a Blockchain Engineer to work on decentralized oracle networks...',
      applyUrl: 'https://jobs.lever.co/chainlink'
    },
    {
      id: 'sample-1-4',
      title: 'Full Stack Developer',
      company: 'Polygon',
      location: 'Remote',
      salary: '$85k - $110k',
      postedTime: '2d',
      tags: ['fullstack', 'react', 'node', 'web3', 'polygon'],
      description: 'Polygon is looking for a Full Stack Developer to build the next generation of Web3 applications...',
      applyUrl: 'https://polygon.technology/careers'
    },
    {
      id: 'sample-1-5',
      title: 'Smart Contract Developer',
      company: 'Aave',
      location: 'London, UK',
      salary: '$100k - $130k',
      postedTime: '3d',
      tags: ['solidity', 'smart-contracts', 'defi', 'ethereum', 'testing'],
      description: 'Aave is hiring a Smart Contract Developer to work on DeFi protocols and lending platforms...',
      applyUrl: 'https://jobs.lever.co/aave'
    },
    {
      id: 'sample-1-6',
      title: 'DevOps Engineer',
      company: 'Uniswap Labs',
      location: 'New York, NY',
      salary: '$90k - $115k',
      postedTime: '4d',
      tags: ['devops', 'kubernetes', 'aws', 'blockchain', 'infrastructure'],
      description: 'Uniswap Labs seeks a DevOps Engineer to manage infrastructure for decentralized exchange protocols...',
      applyUrl: 'https://jobs.uniswap.org/'
    },
    {
      id: 'sample-1-7',
      title: 'Frontend Developer',
      company: 'OpenSea',
      location: 'Remote',
      salary: '$80k - $105k',
      postedTime: '5d',
      tags: ['frontend', 'react', 'typescript', 'nft', 'web3'],
      description: 'OpenSea is looking for a Frontend Developer to build user interfaces for NFT marketplace...',
      applyUrl: 'https://opensea.io/careers'
    },
    {
      id: 'sample-1-8',
      title: 'Backend Engineer',
      company: 'Compound Labs',
      location: 'San Francisco, CA',
      salary: '$95k - $125k',
      postedTime: '6d',
      tags: ['backend', 'node', 'python', 'defi', 'apis'],
      description: 'Compound Labs needs a Backend Engineer to develop APIs and services for DeFi lending protocols...',
      applyUrl: 'https://compound.finance/careers'
    },
    {
      id: 'sample-1-9',
      title: 'Security Engineer',
      company: 'Trail of Bits',
      location: 'Remote',
      salary: '$120k - $150k',
      postedTime: '1w',
      tags: ['security', 'auditing', 'smart-contracts', 'penetration-testing'],
      description: 'Trail of Bits is hiring a Security Engineer specializing in blockchain and smart contract auditing...',
      applyUrl: 'https://www.trailofbits.com/careers'
    },
    {
      id: 'sample-1-10',
      title: 'Data Engineer',
      company: 'The Graph',
      location: 'Remote',
      salary: '$85k - $110k',
      postedTime: '1w',
      tags: ['data', 'graphql', 'indexing', 'blockchain', 'analytics'],
      description: 'The Graph seeks a Data Engineer to work on decentralized indexing and querying infrastructure...',
      applyUrl: 'https://thegraph.com/careers'
    },

    // Page 2 jobs
    {
      id: 'sample-2-1',
      title: 'Senior Solidity Developer',
      company: 'MakerDAO',
      location: 'Remote',
      salary: '$110k - $140k',
      postedTime: '2h',
      tags: ['solidity', 'senior', 'defi', 'dao', 'governance'],
      description: 'MakerDAO is seeking a Senior Solidity Developer to work on decentralized stablecoin protocols...',
      applyUrl: 'https://makerdao.com/careers'
    },
    {
      id: 'sample-2-2',
      title: 'Web3 Product Manager',
      company: 'Coinbase',
      location: 'San Francisco, CA',
      salary: '$130k - $160k',
      postedTime: '4h',
      tags: ['product', 'manager', 'web3', 'crypto', 'strategy'],
      description: 'Coinbase is looking for a Web3 Product Manager to drive product strategy for crypto products...',
      applyUrl: 'https://www.coinbase.com/careers'
    },
    {
      id: 'sample-2-3',
      title: 'Rust Developer',
      company: 'Solana Labs',
      location: 'Remote',
      salary: '$105k - $135k',
      postedTime: '6h',
      tags: ['rust', 'solana', 'blockchain', 'performance', 'systems'],
      description: 'Solana Labs seeks a Rust Developer to work on high-performance blockchain infrastructure...',
      applyUrl: 'https://jobs.solana.com/jobs'
    },
    {
      id: 'sample-2-4',
      title: 'Mobile Developer',
      company: 'MetaMask',
      location: 'Remote',
      salary: '$90k - $120k',
      postedTime: '8h',
      tags: ['mobile', 'react-native', 'ios', 'android', 'wallet'],
      description: 'MetaMask is hiring a Mobile Developer to build the next generation of Web3 mobile wallets...',
      applyUrl: 'https://consensys.io/careers'
    },
    {
      id: 'sample-2-5',
      title: 'QA Engineer',
      company: 'Alchemy',
      location: 'San Francisco, CA',
      salary: '$75k - $95k',
      postedTime: '10h',
      tags: ['qa', 'testing', 'automation', 'blockchain', 'apis'],
      description: 'Alchemy needs a QA Engineer to ensure quality of blockchain infrastructure and APIs...',
      applyUrl: 'https://www.alchemy.com/careers'
    },
    {
      id: 'sample-2-6',
      title: 'Technical Writer',
      company: 'Ethereum Foundation',
      location: 'Remote',
      salary: '$70k - $90k',
      postedTime: '12h',
      tags: ['technical-writing', 'documentation', 'ethereum', 'developer-tools'],
      description: 'Ethereum Foundation seeks a Technical Writer to create developer documentation and guides...',
      applyUrl: 'https://ethereum.org/en/foundation/'
    },
    {
      id: 'sample-2-7',
      title: 'UI/UX Designer',
      company: 'Rarible',
      location: 'Remote',
      salary: '$80k - $100k',
      postedTime: '14h',
      tags: ['ui', 'ux', 'design', 'nft', 'marketplace'],
      description: 'Rarible is looking for a UI/UX Designer to design intuitive interfaces for NFT marketplace...',
      applyUrl: 'https://rarible.com/careers'
    },
    {
      id: 'sample-2-8',
      title: 'Machine Learning Engineer',
      company: 'Numerai',
      location: 'San Francisco, CA',
      salary: '$115k - $145k',
      postedTime: '16h',
      tags: ['ml', 'python', 'data-science', 'crypto', 'prediction'],
      description: 'Numerai seeks an ML Engineer to work on cryptocurrency prediction models and data science...',
      applyUrl: 'https://numer.ai/careers'
    },
    {
      id: 'sample-2-9',
      title: 'Community Manager',
      company: 'Discord',
      location: 'Remote',
      salary: '$60k - $80k',
      postedTime: '18h',
      tags: ['community', 'social', 'discord', 'web3', 'engagement'],
      description: 'Discord is hiring a Community Manager to engage with Web3 communities and developers...',
      applyUrl: 'https://discord.com/careers'
    },
    {
      id: 'sample-2-10',
      title: 'Business Development',
      company: 'Binance',
      location: 'Singapore',
      salary: '$92k - $98k',
      postedTime: '20h',
      tags: ['business', 'partnerships', 'crypto', 'trading', 'growth'],
      description: 'Join Binance Business Development team to drive partnerships and growth in crypto markets...',
      applyUrl: 'https://www.binance.com/en/careers'
    }
  ];

  // Calculate pagination
  const jobsPerPage = 10;
  const startIndex = (page - 1) * jobsPerPage;
  const endIndex = startIndex + jobsPerPage;
  const paginatedJobs = allSampleJobs.slice(startIndex, endIndex);

  return {
    jobs: paginatedJobs,
    totalCount: allSampleJobs.length,
    currentPage: page,
    hasNextPage: endIndex < allSampleJobs.length
  };
}

import axios from 'axios';
import * as cheerio from 'cheerio';
import { Job, JobScrapingResult } from '@/types/job';

const BASE_URL = 'https://web3.career';

export async function scrapeWeb3Jobs(page: number = 1): Promise<JobScrapingResult> {
  try {
    const url = page === 1 ? `${BASE_URL}/java-jobs` : `${BASE_URL}/java-jobs?page=${page}`;
    
    // Note: Due to CORS restrictions, we'll need to use a proxy or server-side solution
    // For now, we'll simulate the data structure and provide a fallback
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    const $ = cheerio.load(response.data);
    const jobs: Job[] = [];

    // Parse job listings from the page
    $('tr').each((index, element) => {
      const $row = $(element);
      
      // Skip header row and empty rows
      if ($row.find('th').length > 0 || $row.find('td').length === 0) {
        return;
      }

      const $cells = $row.find('td');
      if ($cells.length >= 5) {
        const titleElement = $cells.eq(0).find('a').first();
        const companyElement = $cells.eq(1).find('h3');
        const locationText = $cells.eq(3).text().trim();
        const salaryText = $cells.eq(4).text().trim();
        const postedText = $cells.eq(2).text().trim();
        
        // Extract tags
        const tags: string[] = [];
        $cells.eq(5).find('a').each((_, tagEl) => {
          const tagText = $(tagEl).text().trim();
          if (tagText) tags.push(tagText);
        });

        const title = titleElement.text().trim();
        const company = companyElement.text().trim();
        const jobUrl = titleElement.attr('href');
        
        if (title && company && jobUrl) {
          const job: Job = {
            id: `web3-${index}-${Date.now()}`,
            title,
            company,
            location: locationText || 'Remote',
            salary: salaryText || 'Not specified',
            postedTime: postedText || 'Recently',
            tags,
            description: '', // Will be populated when fetching individual job details
            applyUrl: jobUrl.startsWith('http') ? jobUrl : `${BASE_URL}${jobUrl}`,
            companyUrl: companyElement.parent().attr('href')
          };
          
          jobs.push(job);
        }
      }
    });

    // Check for pagination
    const hasNextPage = $('a:contains("Next")').length > 0;
    const totalCountText = $('h1').text();
    const totalMatch = totalCountText.match(/(\d+,?\d*)\s+jobs/);
    const totalCount = totalMatch ? parseInt(totalMatch[1].replace(',', '')) : jobs.length;

    return {
      jobs,
      totalCount,
      currentPage: page,
      hasNextPage
    };

  } catch (error) {
    console.error('Error scraping jobs:', error);
    
    // Fallback: Return sample data for development/testing
    return getSampleJobs(page);
  }
}

export async function fetchJobDescription(jobUrl: string): Promise<string> {
  try {
    const response = await axios.get(jobUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    const $ = cheerio.load(response.data);
    
    // Extract job description - this may need adjustment based on actual HTML structure
    const description = $('.job-description, .description, [class*="description"]').first().text().trim();
    
    return description || 'Job description not available';
  } catch (error) {
    console.error('Error fetching job description:', error);
    return 'Unable to fetch job description';
  }
}

// Fallback sample data for development/testing
function getSampleJobs(page: number): JobScrapingResult {
  const allSampleJobs: Job[] = [
    // Page 1 jobs
    {
      id: 'sample-1-1',
      title: 'Lead Software Engineer',
      company: 'Mastercard',
      location: 'Pune, India',
      salary: '$81k - $84k',
      postedTime: '12h',
      tags: ['engineer', 'lead', 'dev', 'angular', 'blockchain'],
      description: `Lead Software Engineer - Blockchain & Digital Assets

About Mastercard:
Mastercard is a global technology company in the payments industry. Our mission is to connect and power an inclusive, digital economy that benefits everyone, everywhere by making transactions safe, simple, smart and accessible.

Position Overview:
We are seeking a talented Lead Software Development Engineer to join our Mastercard Blockchain and Digital Assets engineering team. You will be responsible for designing, developing, and maintaining cutting-edge blockchain solutions and digital payment systems.

Key Responsibilities:
• Lead the design and development of blockchain-based payment solutions
• Architect scalable microservices using Java, Spring Boot, and Angular
• Collaborate with cross-functional teams to deliver high-quality software
• Mentor junior developers and provide technical guidance
• Implement security best practices for financial applications
• Work with distributed systems and cloud technologies (AWS/Azure)
• Participate in code reviews and maintain coding standards
• Drive innovation in digital payments and blockchain technology

Required Qualifications:
• Bachelor's degree in Computer Science or related field
• 5+ years of software development experience
• Strong proficiency in Java, Spring Framework, and Angular
• Experience with blockchain technologies (Ethereum, Hyperledger, etc.)
• Knowledge of microservices architecture and RESTful APIs
• Experience with cloud platforms (AWS, Azure, or GCP)
• Understanding of financial services and payment systems
• Strong problem-solving and analytical skills
• Excellent communication and leadership abilities

Preferred Qualifications:
• Master's degree in Computer Science or related field
• Experience with cryptocurrency and digital assets
• Knowledge of smart contracts and DeFi protocols
• Experience with DevOps tools and CI/CD pipelines
• Familiarity with regulatory compliance in financial services

What We Offer:
• Competitive salary and comprehensive benefits package
• Opportunity to work on cutting-edge blockchain technology
• Professional development and career growth opportunities
• Flexible work arrangements and work-life balance
• Collaborative and inclusive work environment
• Access to latest technologies and tools

Join us in shaping the future of digital payments and blockchain technology at Mastercard!`,
      applyUrl: 'https://careers.mastercard.com/us/en/search-results'
    },
    {
      id: 'sample-1-2',
      title: 'Senior Java Developer',
      company: 'ConsenSys',
      location: 'Remote',
      salary: '$95k - $120k',
      postedTime: '8h',
      tags: ['java', 'senior', 'blockchain', 'ethereum', 'smart-contracts'],
      description: `Senior Java Developer - Ethereum Infrastructure

About ConsenSys:
ConsenSys is the leading Ethereum software company. We enable developers, enterprises, and people worldwide to build next-generation applications, launch modern financial infrastructure, and access the decentralized web.

Position Overview:
We are looking for a Senior Java Developer to join our team working on Ethereum infrastructure and Web3 applications. You will be building the backbone of decentralized applications and contributing to the future of Web3.

Key Responsibilities:
• Develop and maintain Java-based backend services for Ethereum applications
• Build APIs and microservices that interact with blockchain networks
• Implement smart contract integration and Web3 functionality
• Work with Ethereum protocols and decentralized technologies
• Collaborate with blockchain engineers and frontend developers
• Optimize performance and scalability of distributed systems
• Participate in architectural decisions and technical design reviews
• Contribute to open-source projects and developer tools

Required Qualifications:
• Bachelor's degree in Computer Science or equivalent experience
• 4+ years of Java development experience
• Strong knowledge of Spring Boot, Spring Framework, and Maven/Gradle
• Experience with RESTful APIs and microservices architecture
• Understanding of blockchain technology and Ethereum ecosystem
• Knowledge of smart contracts and Web3 development
• Experience with databases (PostgreSQL, MongoDB)
• Familiarity with cloud platforms (AWS, GCP, or Azure)
• Strong problem-solving and debugging skills

Preferred Qualifications:
• Experience with Solidity and smart contract development
• Knowledge of DeFi protocols and decentralized applications
• Experience with Docker, Kubernetes, and containerization
• Familiarity with GraphQL and modern API design
• Understanding of cryptography and security best practices
• Experience with Ethereum development tools (Truffle, Hardhat, Web3j)
• Contributions to open-source blockchain projects

What We Offer:
• Competitive salary with equity participation
• Fully remote work environment with flexible hours
• Comprehensive health, dental, and vision insurance
• Professional development budget and conference attendance
• Opportunity to work on cutting-edge blockchain technology
• Collaborative team environment with top-tier engineers
• Access to the latest development tools and technologies

Join us in building the decentralized future with Ethereum!`,
      applyUrl: 'https://consensys.io/careers'
    },
    {
      id: 'sample-1-3',
      title: 'Blockchain Engineer',
      company: 'Chainlink Labs',
      location: 'San Francisco, CA',
      salary: '$110k - $140k',
      postedTime: '1d',
      tags: ['blockchain', 'solidity', 'defi', 'oracles', 'smart-contracts'],
      description: `Blockchain Engineer - Decentralized Oracle Networks

About Chainlink Labs:
Chainlink Labs is the primary contributing developer of Chainlink, the decentralized computing platform powering the verifiable web. Chainlink is the industry-standard decentralized computing platform that enhances smart contracts by connecting them to real-world data and off-chain computation.

Position Overview:
We are seeking a talented Blockchain Engineer to join our team working on decentralized oracle networks. You will be responsible for building and maintaining the infrastructure that connects smart contracts to real-world data and external APIs.

Key Responsibilities:
• Design and develop smart contracts for oracle functionality
• Build and maintain decentralized oracle networks
• Implement data feeds and external API integrations
• Work on cross-chain interoperability solutions
• Develop tools and SDKs for developers using Chainlink
• Optimize gas efficiency and performance of smart contracts
• Collaborate with research team on new protocol features
• Participate in security audits and code reviews

Required Qualifications:
• Bachelor's degree in Computer Science, Engineering, or related field
• 3+ years of blockchain development experience
• Strong proficiency in Solidity and smart contract development
• Experience with Ethereum, Polygon, and other EVM-compatible chains
• Knowledge of DeFi protocols and decentralized applications
• Understanding of oracle networks and external data integration
• Experience with Web3 development tools (Hardhat, Truffle, Foundry)
• Proficiency in JavaScript/TypeScript and Node.js
• Strong understanding of cryptography and security principles

Preferred Qualifications:
• Experience with Go, Rust, or other systems programming languages
• Knowledge of cross-chain protocols and bridge technologies
• Experience with cloud infrastructure (AWS, GCP, Azure)
• Understanding of consensus mechanisms and distributed systems
• Experience with DevOps tools and CI/CD pipelines
• Contributions to open-source blockchain projects
• Knowledge of traditional finance and derivatives markets

What We Offer:
• Competitive salary with significant equity upside
• Comprehensive health, dental, and vision insurance
• Flexible PTO and remote work options
• Professional development and conference budget
• Top-tier equipment and development tools
• Opportunity to work on industry-leading oracle technology
• Collaborative environment with world-class engineers
• Access to cutting-edge research and development

Help us build the infrastructure for the verifiable web!`,
      applyUrl: 'https://jobs.lever.co/chainlink'
    },
    {
      id: 'sample-1-4',
      title: 'Full Stack Developer',
      company: 'Polygon',
      location: 'Remote',
      salary: '$85k - $110k',
      postedTime: '2d',
      tags: ['fullstack', 'react', 'node', 'web3', 'polygon'],
      description: `Full Stack Developer - Web3 Applications

About Polygon:
Polygon is a leading platform for Ethereum scaling and infrastructure development. Our mission is to bring the world to Ethereum by providing scalable, secure, and instant blockchain transactions.

Position Overview:
We are looking for a Full Stack Developer to build the next generation of Web3 applications on Polygon. You will work on both frontend and backend systems that power decentralized applications and developer tools.

Key Responsibilities:
• Develop full-stack Web3 applications using React and Node.js
• Build user interfaces for decentralized applications (dApps)
• Implement smart contract integration and Web3 wallet connectivity
• Create APIs and backend services for blockchain interactions
• Work with Polygon SDK and developer tools
• Optimize application performance and user experience
• Collaborate with design and product teams
• Participate in code reviews and maintain high code quality

Required Qualifications:
• Bachelor's degree in Computer Science or equivalent experience
• 3+ years of full-stack development experience
• Strong proficiency in React, JavaScript/TypeScript, and Node.js
• Experience with Web3 technologies and blockchain integration
• Knowledge of smart contracts and Ethereum ecosystem
• Experience with RESTful APIs and database design
• Familiarity with modern development tools and workflows
• Understanding of responsive design and mobile-first development

Preferred Qualifications:
• Experience with Polygon network and Layer 2 solutions
• Knowledge of Solidity and smart contract development
• Experience with Web3 libraries (ethers.js, web3.js)
• Familiarity with DeFi protocols and NFT marketplaces
• Experience with cloud platforms and DevOps practices
• Knowledge of GraphQL and modern API design patterns
• Understanding of blockchain scalability solutions

What We Offer:
• Competitive salary with token incentives
• Fully remote work with flexible hours
• Comprehensive health and wellness benefits
• Professional development opportunities
• Access to cutting-edge blockchain technology
• Collaborative and innovative work environment
• Opportunity to shape the future of Web3

Join us in scaling Ethereum for mass adoption!`,
      applyUrl: 'https://polygon.technology/careers'
    },
    {
      id: 'sample-1-5',
      title: 'Smart Contract Developer',
      company: 'Aave',
      location: 'London, UK',
      salary: '$100k - $130k',
      postedTime: '3d',
      tags: ['solidity', 'smart-contracts', 'defi', 'ethereum', 'testing'],
      description: `Smart Contract Developer - DeFi Protocols

About Aave:
Aave is a decentralized non-custodial liquidity market protocol where users can participate as depositors or borrowers. We're building the future of decentralized finance.

Position Overview:
We are hiring a Smart Contract Developer to work on DeFi protocols and lending platforms. You will be responsible for developing, testing, and maintaining smart contracts that handle billions of dollars in value.

Key Responsibilities:
• Develop and deploy smart contracts for DeFi lending protocols
• Implement new features for Aave protocol and governance
• Conduct thorough testing and security audits of smart contracts
• Optimize gas efficiency and contract performance
• Collaborate with security teams on protocol safety
• Work on cross-chain integrations and Layer 2 solutions
• Participate in protocol governance and improvement proposals
• Maintain comprehensive documentation and code standards

Required Qualifications:
• 2+ years of Solidity and smart contract development experience
• Strong understanding of DeFi protocols and lending mechanisms
• Experience with testing frameworks (Hardhat, Foundry, Brownie)
• Knowledge of security best practices and common vulnerabilities
• Understanding of Ethereum Virtual Machine (EVM) and gas optimization
• Experience with version control and collaborative development
• Strong analytical and problem-solving skills

Preferred Qualifications:
• Experience with Aave protocol or similar DeFi platforms
• Knowledge of formal verification and security auditing
• Experience with multi-chain development (Polygon, Avalanche, etc.)
• Understanding of tokenomics and governance mechanisms
• Contributions to open-source DeFi projects

What We Offer:
• Competitive salary with AAVE token incentives
• Remote-first culture with flexible working hours
• Comprehensive health and wellness benefits
• Professional development and conference budget
• Opportunity to work on cutting-edge DeFi technology
• Collaborative team of world-class developers

Help us build the future of decentralized finance!`,
      applyUrl: 'https://jobs.lever.co/aave'
    },
    {
      id: 'sample-1-6',
      title: 'DevOps Engineer',
      company: 'Uniswap Labs',
      location: 'New York, NY',
      salary: '$90k - $115k',
      postedTime: '4d',
      tags: ['devops', 'kubernetes', 'aws', 'blockchain', 'infrastructure'],
      description: `DevOps Engineer - Decentralized Exchange Infrastructure

About Uniswap Labs:
Uniswap Labs is the team behind the Uniswap Protocol, the largest decentralized exchange on Ethereum.

Position Overview:
We seek a DevOps Engineer to manage infrastructure for decentralized exchange protocols and ensure high availability of our systems.

Key Responsibilities:
• Manage cloud infrastructure for high-traffic DeFi applications
• Implement CI/CD pipelines for smart contract and frontend deployments
• Monitor and optimize system performance and reliability
• Ensure security best practices across all infrastructure
• Collaborate with development teams on deployment strategies
• Manage containerized applications using Docker and Kubernetes
• Implement infrastructure as code using Terraform or similar tools

Required Qualifications:
• 3+ years of DevOps or infrastructure engineering experience
• Strong knowledge of AWS, GCP, or Azure cloud platforms
• Experience with Kubernetes, Docker, and containerization
• Proficiency in scripting languages (Python, Bash, etc.)
• Understanding of blockchain infrastructure and Web3 applications
• Experience with monitoring tools (Prometheus, Grafana, etc.)
• Knowledge of CI/CD tools (Jenkins, GitHub Actions, etc.)

What We Offer:
• Competitive salary with UNI token incentives
• Remote work flexibility
• Comprehensive benefits package
• Opportunity to work on leading DeFi infrastructure`,
      applyUrl: 'https://jobs.uniswap.org/'
    },
    {
      id: 'sample-1-7',
      title: 'Frontend Developer',
      company: 'OpenSea',
      location: 'Remote',
      salary: '$80k - $105k',
      postedTime: '5d',
      tags: ['frontend', 'react', 'typescript', 'nft', 'web3'],
      description: `Frontend Developer - NFT Marketplace

About OpenSea:
OpenSea is the world's first and largest digital marketplace for crypto collectibles and non-fungible tokens (NFTs).

Position Overview:
We are looking for a Frontend Developer to build user interfaces for our NFT marketplace and create exceptional user experiences.

Key Responsibilities:
• Develop responsive web applications using React and TypeScript
• Build intuitive interfaces for NFT discovery, trading, and collection management
• Integrate with Web3 wallets and blockchain networks
• Optimize application performance and user experience
• Collaborate with designers and product teams
• Implement real-time features for live auctions and bidding
• Ensure cross-browser compatibility and mobile responsiveness

Required Qualifications:
• 3+ years of frontend development experience
• Strong proficiency in React, JavaScript/TypeScript, and CSS
• Experience with Web3 integration and wallet connectivity
• Knowledge of responsive design and modern frontend tools
• Understanding of NFTs and blockchain technology
• Experience with state management (Redux, Zustand, etc.)

What We Offer:
• Competitive salary with equity participation
• Remote work options and flexible hours
• Comprehensive health and wellness benefits
• Opportunity to work on the leading NFT platform`,
      applyUrl: 'https://opensea.io/careers'
    },
    {
      id: 'sample-1-8',
      title: 'Backend Engineer',
      company: 'Compound Labs',
      location: 'San Francisco, CA',
      salary: '$95k - $125k',
      postedTime: '6d',
      tags: ['backend', 'node', 'python', 'defi', 'apis'],
      description: `Backend Engineer - DeFi Lending Protocols

About Compound Labs:
Compound is an algorithmic, autonomous interest rate protocol built for developers, to unlock a universe of open financial applications.

Position Overview:
We need a Backend Engineer to develop APIs and services for DeFi lending protocols, handling billions in total value locked.

Key Responsibilities:
• Build scalable backend services for DeFi lending protocols
• Develop APIs for interest rate calculations and liquidity management
• Implement real-time data processing for blockchain events
• Design and maintain databases for financial data
• Ensure high availability and performance of critical systems
• Collaborate with smart contract developers on protocol integration
• Implement security measures for financial applications

Required Qualifications:
• 3+ years of backend development experience (Node.js, Python, or Go)
• Experience with databases (PostgreSQL, MongoDB) and caching systems
• Knowledge of DeFi protocols and blockchain technology
• Understanding of financial markets and lending mechanisms
• Experience with cloud platforms and microservices architecture
• Strong problem-solving and analytical skills

What We Offer:
• Competitive salary with COMP token incentives
• Remote work flexibility and comprehensive benefits
• Opportunity to work on leading DeFi lending protocol`,
      applyUrl: 'https://compound.finance/careers'
    },
    {
      id: 'sample-1-9',
      title: 'Security Engineer',
      company: 'Trail of Bits',
      location: 'Remote',
      salary: '$120k - $150k',
      postedTime: '1w',
      tags: ['security', 'auditing', 'smart-contracts', 'penetration-testing'],
      description: `Security Engineer - Blockchain & Smart Contract Auditing

About Trail of Bits:
Trail of Bits is a cybersecurity research and consulting company focused on software assurance, blockchain security, and applied cryptography.

Position Overview:
We are hiring a Security Engineer specializing in blockchain and smart contract auditing to help secure the future of Web3.

Key Responsibilities:
• Conduct security audits of smart contracts and blockchain protocols
• Perform penetration testing on Web3 applications and infrastructure
• Develop automated security testing tools and frameworks
• Research new attack vectors and security vulnerabilities
• Write detailed security reports and recommendations
• Collaborate with development teams on security best practices
• Contribute to open-source security tools and research

Required Qualifications:
• 3+ years of cybersecurity or security engineering experience
• Strong knowledge of Solidity and smart contract security
• Experience with security auditing and penetration testing
• Understanding of blockchain technology and DeFi protocols
• Knowledge of cryptography and security principles
• Experience with security tools (static analysis, fuzzing, etc.)
• Strong technical writing and communication skills

What We Offer:
• Competitive salary with performance bonuses
• Remote work with flexible hours
• Professional development and research opportunities
• Access to cutting-edge security tools and technologies`,
      applyUrl: 'https://www.trailofbits.com/careers'
    },
    {
      id: 'sample-1-10',
      title: 'Data Engineer',
      company: 'The Graph',
      location: 'Remote',
      salary: '$85k - $110k',
      postedTime: '1w',
      tags: ['data', 'graphql', 'indexing', 'blockchain', 'analytics'],
      description: `Data Engineer - Decentralized Indexing Infrastructure

About The Graph:
The Graph is an indexing protocol for querying networks like Ethereum and IPFS, making it possible to query data that was previously difficult to access.

Position Overview:
We seek a Data Engineer to work on decentralized indexing and querying infrastructure that powers thousands of dApps.

Key Responsibilities:
• Build and maintain data indexing infrastructure for blockchain networks
• Develop GraphQL APIs for efficient data querying
• Optimize data processing pipelines for real-time blockchain events
• Design scalable data storage and retrieval systems
• Work with subgraph developers and dApp teams
• Implement data validation and quality assurance processes
• Monitor and optimize query performance across the network

Required Qualifications:
• 3+ years of data engineering experience
• Strong knowledge of GraphQL and API design
• Experience with big data technologies (Apache Kafka, Spark, etc.)
• Understanding of blockchain technology and data structures
• Proficiency in programming languages (TypeScript, Rust, or Go)
• Experience with databases and data warehousing solutions
• Knowledge of distributed systems and microservices

What We Offer:
• Competitive salary with GRT token incentives
• Remote-first culture with global team collaboration
• Comprehensive benefits and professional development budget
• Opportunity to work on critical Web3 infrastructure`,
      applyUrl: 'https://thegraph.com/careers'
    },

    // Page 2 jobs
    {
      id: 'sample-2-1',
      title: 'Senior Solidity Developer',
      company: 'MakerDAO',
      location: 'Remote',
      salary: '$110k - $140k',
      postedTime: '2h',
      tags: ['solidity', 'senior', 'defi', 'dao', 'governance'],
      description: 'MakerDAO is seeking a Senior Solidity Developer to work on decentralized stablecoin protocols...',
      applyUrl: 'https://makerdao.com/careers'
    },
    {
      id: 'sample-2-2',
      title: 'Web3 Product Manager',
      company: 'Coinbase',
      location: 'San Francisco, CA',
      salary: '$130k - $160k',
      postedTime: '4h',
      tags: ['product', 'manager', 'web3', 'crypto', 'strategy'],
      description: 'Coinbase is looking for a Web3 Product Manager to drive product strategy for crypto products...',
      applyUrl: 'https://www.coinbase.com/careers'
    },
    {
      id: 'sample-2-3',
      title: 'Rust Developer',
      company: 'Solana Labs',
      location: 'Remote',
      salary: '$105k - $135k',
      postedTime: '6h',
      tags: ['rust', 'solana', 'blockchain', 'performance', 'systems'],
      description: 'Solana Labs seeks a Rust Developer to work on high-performance blockchain infrastructure...',
      applyUrl: 'https://jobs.solana.com/jobs'
    },
    {
      id: 'sample-2-4',
      title: 'Mobile Developer',
      company: 'MetaMask',
      location: 'Remote',
      salary: '$90k - $120k',
      postedTime: '8h',
      tags: ['mobile', 'react-native', 'ios', 'android', 'wallet'],
      description: 'MetaMask is hiring a Mobile Developer to build the next generation of Web3 mobile wallets...',
      applyUrl: 'https://consensys.io/careers'
    },
    {
      id: 'sample-2-5',
      title: 'QA Engineer',
      company: 'Alchemy',
      location: 'San Francisco, CA',
      salary: '$75k - $95k',
      postedTime: '10h',
      tags: ['qa', 'testing', 'automation', 'blockchain', 'apis'],
      description: 'Alchemy needs a QA Engineer to ensure quality of blockchain infrastructure and APIs...',
      applyUrl: 'https://www.alchemy.com/careers'
    },
    {
      id: 'sample-2-6',
      title: 'Technical Writer',
      company: 'Ethereum Foundation',
      location: 'Remote',
      salary: '$70k - $90k',
      postedTime: '12h',
      tags: ['technical-writing', 'documentation', 'ethereum', 'developer-tools'],
      description: 'Ethereum Foundation seeks a Technical Writer to create developer documentation and guides...',
      applyUrl: 'https://ethereum.org/en/foundation/'
    },
    {
      id: 'sample-2-7',
      title: 'UI/UX Designer',
      company: 'Rarible',
      location: 'Remote',
      salary: '$80k - $100k',
      postedTime: '14h',
      tags: ['ui', 'ux', 'design', 'nft', 'marketplace'],
      description: 'Rarible is looking for a UI/UX Designer to design intuitive interfaces for NFT marketplace...',
      applyUrl: 'https://rarible.com/careers'
    },
    {
      id: 'sample-2-8',
      title: 'Machine Learning Engineer',
      company: 'Numerai',
      location: 'San Francisco, CA',
      salary: '$115k - $145k',
      postedTime: '16h',
      tags: ['ml', 'python', 'data-science', 'crypto', 'prediction'],
      description: 'Numerai seeks an ML Engineer to work on cryptocurrency prediction models and data science...',
      applyUrl: 'https://numer.ai/careers'
    },
    {
      id: 'sample-2-9',
      title: 'Community Manager',
      company: 'Discord',
      location: 'Remote',
      salary: '$60k - $80k',
      postedTime: '18h',
      tags: ['community', 'social', 'discord', 'web3', 'engagement'],
      description: 'Discord is hiring a Community Manager to engage with Web3 communities and developers...',
      applyUrl: 'https://discord.com/careers'
    },
    {
      id: 'sample-2-10',
      title: 'Business Development',
      company: 'Binance',
      location: 'Singapore',
      salary: '$92k - $98k',
      postedTime: '20h',
      tags: ['business', 'partnerships', 'crypto', 'trading', 'growth'],
      description: 'Join Binance Business Development team to drive partnerships and growth in crypto markets...',
      applyUrl: 'https://www.binance.com/en/careers'
    }
  ];

  // Calculate pagination
  const jobsPerPage = 10;
  const startIndex = (page - 1) * jobsPerPage;
  const endIndex = startIndex + jobsPerPage;
  const paginatedJobs = allSampleJobs.slice(startIndex, endIndex);

  return {
    jobs: paginatedJobs,
    totalCount: allSampleJobs.length,
    currentPage: page,
    hasNextPage: endIndex < allSampleJobs.length
  };
}

// Job API Configuration
// This file contains configuration for various job APIs that provide real job listings

export const JOB_API_CONFIG = {
  // RemoteOK - Free API for remote jobs (CORS-enabled)
  remoteOk: {
    baseUrl: 'https://remoteok.io/api',
    enabled: true,
    requiresAuth: false,
    description: 'Remote jobs API with blockchain/crypto filter'
  },

  // The Muse - Free API for tech company jobs
  themuse: {
    baseUrl: 'https://www.themuse.com/api/public/jobs',
    enabled: true,
    requiresAuth: false,
    description: 'Tech company jobs with engineering focus'
  },

  // Arbeitnow - European tech jobs API
  arbeitnow: {
    baseUrl: 'https://www.arbeitnow.com/api/job-board-api',
    enabled: true,
    requiresAuth: false,
    description: 'European tech jobs including blockchain'
  },

  // JSearch via RapidAPI - Requires API key
  jsearch: {
    baseUrl: 'https://jsearch.p.rapidapi.com/search',
    enabled: false, // Disabled until API key is provided
    requiresAuth: true,
    apiKey: import.meta.env.VITE_RAPIDAPI_KEY,
    headers: {
      'X-RapidAPI-Key': import.meta.env.VITE_RAPIDAPI_KEY,
      'X-RapidAPI-Host': 'jsearch.p.rapidapi.com'
    },
    description: 'Comprehensive job search via RapidAPI'
  },

  // Adzuna - Requires API key but has free tier
  adzuna: {
    baseUrl: 'https://api.adzuna.com/v1/api/jobs/us/search',
    enabled: false, // Disabled until API key is provided
    requiresAuth: true,
    appId: import.meta.env.VITE_ADZUNA_APP_ID,
    appKey: import.meta.env.VITE_ADZUNA_APP_KEY,
    description: 'Adzuna job search API with free tier'
  },

  // GitHub Jobs Alternative - Jobs from GitHub repositories
  githubjobs: {
    baseUrl: 'https://jobs.github.com/positions.json',
    enabled: false, // GitHub Jobs API was discontinued
    requiresAuth: false,
    description: 'GitHub Jobs (discontinued but kept for reference)'
  }
};

// Web3/Blockchain keywords for filtering jobs
export const WEB3_KEYWORDS = [
  'blockchain', 'crypto', 'cryptocurrency', 'web3', 'ethereum', 'bitcoin',
  'defi', 'nft', 'solidity', 'smart contract', 'dapp', 'decentralized',
  'polygon', 'chainlink', 'uniswap', 'aave', 'compound', 'opensea',
  'metamask', 'wallet', 'dao', 'yield farming', 'liquidity mining',
  'consensus', 'proof of stake', 'proof of work', 'layer 2', 'scaling',
  'interoperability', 'cross-chain', 'bridge', 'oracle', 'tokenomics',
  'governance token', 'staking', 'mining', 'validator', 'node operator'
];

// Company domains for direct career page links
export const COMPANY_CAREER_PAGES = {
  'coinbase': 'https://www.coinbase.com/careers',
  'binance': 'https://www.binance.com/en/careers',
  'opensea': 'https://opensea.io/careers',
  'polygon': 'https://polygon.technology/careers',
  'chainlink': 'https://jobs.lever.co/chainlink',
  'uniswap': 'https://jobs.uniswap.org/',
  'aave': 'https://jobs.lever.co/aave',
  'compound': 'https://compound.finance/careers',
  'consensys': 'https://consensys.io/careers',
  'ethereum': 'https://ethereum.org/en/foundation/',
  'solana': 'https://jobs.solana.com/jobs',
  'avalanche': 'https://www.avalabs.org/careers',
  'near': 'https://careers.near.org/',
  'cosmos': 'https://cosmos.network/careers/',
  'polkadot': 'https://polkadot.network/jobs/',
  'algorand': 'https://www.algorand.com/about/careers',
  'cardano': 'https://iohk.io/en/careers/',
  'tezos': 'https://tezos.com/careers/',
  'flow': 'https://www.onflow.org/careers',
  'hedera': 'https://hedera.com/careers'
};

// Rate limiting configuration
export const API_RATE_LIMITS = {
  remoteOk: { requestsPerMinute: 60, requestsPerHour: 1000 },
  themuse: { requestsPerMinute: 100, requestsPerHour: 5000 },
  arbeitnow: { requestsPerMinute: 60, requestsPerHour: 1000 },
  jsearch: { requestsPerMinute: 100, requestsPerHour: 10000 }, // With API key
  adzuna: { requestsPerMinute: 200, requestsPerHour: 5000 } // With API key
};

// API status and health check
export const checkApiHealth = async (apiName: keyof typeof JOB_API_CONFIG) => {
  const config = JOB_API_CONFIG[apiName];
  if (!config.enabled) {
    return { status: 'disabled', message: 'API is disabled' };
  }

  try {
    const response = await fetch(config.baseUrl, { 
      method: 'HEAD',
      signal: AbortSignal.timeout(5000) // 5 second timeout
    });
    
    return {
      status: response.ok ? 'healthy' : 'error',
      statusCode: response.status,
      message: response.ok ? 'API is responding' : `HTTP ${response.status}`
    };
  } catch (error) {
    return {
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Get enabled APIs
export const getEnabledApis = () => {
  return Object.entries(JOB_API_CONFIG)
    .filter(([_, config]) => config.enabled)
    .map(([name, config]) => ({ name, ...config }));
};

// Environment variable validation
export const validateApiKeys = () => {
  const warnings: string[] = [];
  
  if (!import.meta.env.VITE_RAPIDAPI_KEY) {
    warnings.push('VITE_RAPIDAPI_KEY not set - JSearch API disabled');
  }
  
  if (!import.meta.env.VITE_ADZUNA_APP_ID || !import.meta.env.VITE_ADZUNA_APP_KEY) {
    warnings.push('VITE_ADZUNA_APP_ID/VITE_ADZUNA_APP_KEY not set - Adzuna API disabled');
  }
  
  return warnings;
};
